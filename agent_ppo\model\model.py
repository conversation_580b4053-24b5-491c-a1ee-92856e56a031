#!/usr/bin/env python3
# -*- coding: UTF-8 -*-
###########################################################################
# Copyright © 1998 - 2025 Tencent. All Rights Reserved.
###########################################################################
"""
Author: Tencent AI Arena Authors
"""


from typing import List
import torch
from torch import nn
import numpy as np
from agent_ppo.conf.conf import Config

import sys
import os

if os.path.basename(sys.argv[0]) == "learner.py":
    import torch

    torch.set_num_interop_threads(2)
    torch.set_num_threads(2)
else:
    import torch

    torch.set_num_interop_threads(4)
    torch.set_num_threads(4)


class NetworkModelBase(nn.Module):
    def __init__(self):
        super().__init__()
        # feature configure parameter
        # 特征配置参数
        self.data_split_shape = Config.DATA_SPLIT_SHAPE
        self.feature_split_shape = Config.FEATURE_SPLIT_SHAPE
        self.label_size = Config.ACTION_NUM
        self.feature_len = Config.FEATURE_LEN
        self.value_num = Config.VALUE_NUM

        self.var_beta = Config.BETA_START
        self.vf_coef = Config.VF_COEF

        self.clip_param = Config.CLIP_PARAM

        self.data_len = Config.data_len

        # 空間特徵配置
        self.spatial_channels = Config.SPATIAL_CHANNELS
        self.spatial_height = Config.SPATIAL_HEIGHT
        self.spatial_width = Config.SPATIAL_WIDTH
        self.vector_feature_size = Config.VECTOR_FEATURE_SIZE

        # CNN分支：處理空間特徵 (6, 11, 11)
        self.cnn_branch = nn.Sequential(
            nn.Conv2d(self.spatial_channels, 32, kernel_size=3, padding=1),  # (32, 11, 11)
            nn.ReLU(),
            nn.Conv2d(32, 64, kernel_size=3, padding=1),  # (64, 11, 11)
            nn.ReLU(),
            nn.Conv2d(64, 64, kernel_size=3, stride=2, padding=1),  # (64, 6, 6)
            nn.ReLU(),
            nn.Conv2d(64, 128, kernel_size=3, padding=1),  # (128, 6, 6)
            nn.ReLU(),
            nn.AdaptiveAvgPool2d((3, 3)),  # (128, 3, 3)
            nn.Flatten(),  # 128 * 3 * 3 = 1152
        )

        # MLP分支：處理向量特徵
        self.mlp_branch = MLP([self.vector_feature_size, 128, 256], "vector_mlp", non_linearity_last=True)

        # 合併後的網絡
        combined_size = 1152 + 256  # CNN輸出 + MLP輸出
        self.combined_mlp = MLP([combined_size, 512, 256], "combined_mlp", non_linearity_last=True)

        # 輸出頭
        self.label_mlp = MLP([256, 128, self.label_size], "label_mlp")
        self.value_mlp = MLP([256, 128, self.value_num], "value_mlp")

    def process_legal_action(self, label, legal_action):
        label_max, _ = torch.max(label * legal_action, 1, True)
        label = label - label_max
        label = label * legal_action
        label = label + 1e5 * (legal_action - 1)
        return label

    def forward(self, spatial_features, vector_features, legal_action):
        # CNN分支處理空間特徵
        # spatial_features: (batch_size, 6, 11, 11)
        cnn_out = self.cnn_branch(spatial_features)  # (batch_size, 1152)

        # MLP分支處理向量特徵
        # vector_features: (batch_size, 38)
        mlp_out = self.mlp_branch(vector_features)  # (batch_size, 256)

        # 合併兩個分支的輸出
        combined_features = torch.cat([cnn_out, mlp_out], dim=1)  # (batch_size, 1152+256)
        combined_out = self.combined_mlp(combined_features)  # (batch_size, 256)

        # Action and value processing
        # 处理动作和值
        label_mlp_out = self.label_mlp(combined_out)
        label_out = self.process_legal_action(label_mlp_out, legal_action)

        prob = torch.nn.functional.softmax(label_out, dim=1)
        value = self.value_mlp(combined_out)

        return prob, value


class NetworkModelActor(NetworkModelBase):
    def format_data(self, spatial_features, vector_features, legal_action):
        # spatial_features: (6, 11, 11) -> (1, 6, 11, 11)
        # vector_features: (38,) -> (1, 38)
        # legal_action: (16,) -> (1, 16)
        return (
            torch.tensor(spatial_features).unsqueeze(0).to(torch.float32),
            torch.tensor(vector_features).unsqueeze(0).to(torch.float32),
            torch.tensor(legal_action).unsqueeze(0).to(torch.float32),
        )


class NetworkModelLearner(NetworkModelBase):
    def format_data(self, datas):
        # 分割數據：[spatial_features, vector_features, legal_action]
        data_list = datas.view(-1, self.data_len).float().split(self.data_split_shape, dim=1)

        # 重塑空間特徵為 (batch_size, channels, height, width)
        spatial_flat = data_list[0]  # (batch_size, 726)
        batch_size = spatial_flat.shape[0]
        spatial_features = spatial_flat.view(batch_size, self.spatial_channels,
                                           self.spatial_height, self.spatial_width)

        vector_features = data_list[1]  # (batch_size, 38)
        legal_action = data_list[2]     # (batch_size, 16)

        return spatial_features, vector_features, legal_action

    def forward(self, data_list, inference=False):
        spatial_features, vector_features, legal_action = data_list
        return super().forward(spatial_features, vector_features, legal_action)


def make_fc_layer(in_features: int, out_features: int):
    # Wrapper function to create and initialize a linear layer
    # 创建并初始化一个线性层
    fc_layer = nn.Linear(in_features, out_features)

    # initialize weight and bias
    # 初始化权重及偏移量
    nn.init.orthogonal(fc_layer.weight)
    nn.init.zeros_(fc_layer.bias)

    return fc_layer


class MLP(nn.Module):
    def __init__(
        self,
        fc_feat_dim_list: List[int],
        name: str,
        non_linearity: nn.Module = nn.ReLU,
        non_linearity_last: bool = False,
    ):
        # Create a MLP object
        # 创建一个 MLP 对象
        super().__init__()
        self.fc_layers = nn.Sequential()
        for i in range(len(fc_feat_dim_list) - 1):
            fc_layer = make_fc_layer(fc_feat_dim_list[i], fc_feat_dim_list[i + 1])
            self.fc_layers.add_module("{0}_fc{1}".format(name, i + 1), fc_layer)
            # no relu for the last fc layer of the mlp unless required
            # 除非有需要，否则 mlp 的最后一个 fc 层不使用 relu
            if i + 1 < len(fc_feat_dim_list) - 1 or non_linearity_last:
                self.fc_layers.add_module("{0}_non_linear{1}".format(name, i + 1), non_linearity())

    def forward(self, data):
        return self.fc_layers(data)
